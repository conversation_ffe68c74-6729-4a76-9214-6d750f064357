.navbar {
    // height: 5.4rem;
    width: 100%;
    background-color: var(--color-primary);
    border-radius: 1rem;
    color: white;
    padding-top: 1rem;
    padding-bottom: 1rem;
    font-size: 1.4rem;
}

.sub-navbar {
    display: flex;
    align-items: center;
    margin: auto;
    width: 80%;
    background-color: var(--color-primary);
    border-radius: 1rem;
    color: white;
}

.round-image-container {
    width: 9rem;
    height: 9rem;
    overflow: hidden;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    bottom: -4rem;

    &:lang(en) {
        right: 1.7rem;
    }

    &:lang(ar) {
        left: 1.7rem;
    }
}

.round-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
@media screen and (max-width: 600px) {
    .round-image-container  {
        bottom: -7rem;
    }
}
