// Reports Container
.reports-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 2rem;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 50%, rgba(29, 76, 186, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }
}

// Header Section
.reports-header {
  margin-bottom: 2rem;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;

  .title-text {
    color: var(--color-primary);
    font-size: 2.4rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .title-icon {
    font-size: 2.8rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }
}

// Form Section
.form-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1.5rem;
  padding: 2rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.reports-form {
  width: 100%;
}

.filters-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.filter-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  font-weight: 600;
  color: var(--color-primary);
  font-size: 1.4rem;

  .label-icon {
    font-size: 1.6rem;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
}

.input-wrapper {
  position: relative;
}

// Date Filters
.date-filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.date-item {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.error-message {
  color: #ef4444;
  font-size: 1.2rem;
  margin-top: 0.5rem;
  font-weight: 500;
}

// Submit Section
.submit-section {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.submit-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: linear-gradient(135deg, var(--color-primary) 0%, rgba(29, 76, 186, 0.9) 100%);
  color: white;
  border: none;
  border-radius: 1.2rem;
  padding: 1.2rem 3rem;
  font-size: 1.4rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(29, 76, 186, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(29, 76, 186, 0.4);
    background: linear-gradient(135deg, rgba(29, 76, 186, 0.9) 0%, var(--color-primary) 100%);
  }

  &:active {
    transform: translateY(0);
  }

  .btn-icon {
    font-size: 1.6rem;
  }
}

/* Custom MultiSelect Styles */
::ng-deep .custom-multiselect {
  .p-multiselect {
    width: 100%;
    border-radius: 1rem;
    border: 2px solid rgba(29, 76, 186, 0.2);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(29, 76, 186, 0.4);
      box-shadow: 0 4px 15px rgba(29, 76, 186, 0.1);
    }

    &.p-focus {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 3px rgba(29, 76, 186, 0.1);
    }
  }

  .p-multiselect-label {
    font-size: 1.4rem;
    padding: 1rem 1.2rem;
    color: #374151;
  }

  .p-multiselect-item {
    font-size: 1.4rem;
    padding: 0.8rem 1.2rem;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(29, 76, 186, 0.1);
    }
  }

  .p-multiselect-panel {
    min-width: 280px;
    border-radius: 1rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}

  .p-multiselect-filter-container {
    display: flex;
    align-items: center;
    padding: 0.8rem 1.2rem;
    gap: 0.8rem;
  }

  .p-multiselect-filter {
    flex: 1;
    height: 2.8rem;
    font-size: 1.4rem;
    padding: 0.8rem 1rem;
    border-radius: 0.8rem;
    border: 1px solid rgba(29, 76, 186, 0.2);

    &:focus {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(29, 76, 186, 0.1);
    }
  }

  .p-multiselect-filter-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    color: var(--color-primary);

    svg {
      width: 1.4rem;
      height: 1.4rem;
    }
  }
}

/* Date Picker Styles */
::ng-deep .date-picker {
  .ant-picker {
    width: 100%;
    height: 4.5rem;
    border-radius: 1rem;
    border: 2px solid rgba(29, 76, 186, 0.2);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(29, 76, 186, 0.4);
      box-shadow: 0 4px 15px rgba(29, 76, 186, 0.1);
    }

    &.ant-picker-focused {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 3px rgba(29, 76, 186, 0.1);
    }
  }

  .ant-picker-input input {
    font-size: 1.4rem;
    padding: 1rem 1.2rem;
  }
}

// Table Section
.table-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1.5rem;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.table-header {
  margin-bottom: 1.5rem;

  .table-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--color-primary);
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;

    .table-icon {
      font-size: 2rem;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }
  }
}

// Desktop Table
.desktop-table {
  display: block;

  ::ng-deep .responsive-table {
    .ant-table {
      border-radius: 1rem;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .ant-table-thead > tr > th {
      background: linear-gradient(135deg, var(--color-primary) 0%, rgba(29, 76, 186, 0.9) 100%);
      color: white;
      font-weight: 600;
      font-size: 1.3rem;
      padding: 1.5rem 1rem;
      border: none;
      text-align: center;
    }

    .ant-table-tbody > tr > td {
      padding: 1.2rem 1rem;
      font-size: 1.2rem;
      text-align: center;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease;
    }

    .ant-table-tbody > tr:hover > td {
      background: rgba(29, 76, 186, 0.05);
    }

    .ant-table-tbody > tr:nth-child(even) > td {
      background: rgba(248, 250, 252, 0.8);
    }
  }
}

// Mobile Cards
.mobile-cards {
  display: none;
}

.report-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 1.2rem;
  padding: 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  .document-number {
    font-weight: 700;
    font-size: 1.4rem;
    color: var(--color-primary);
  }

  .document-type {
    background: rgba(29, 76, 186, 0.1);
    color: var(--color-primary);
    padding: 0.4rem 0.8rem;
    border-radius: 0.6rem;
    font-size: 1.1rem;
    font-weight: 500;
  }
}

.card-body {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.info-row,
.date-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  .label {
    font-weight: 600;
    color: #6b7280;
    font-size: 1.1rem;
    min-width: 40%;
  }

  .value {
    font-weight: 500;
    color: #374151;
    font-size: 1.1rem;
    text-align: right;
    flex: 1;
  }
}

.amounts-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.amount-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border-radius: 0.8rem;

  &.debit {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);

    .amount-label {
      color: #dc2626;
    }

    .amount-value {
      color: #b91c1c;
    }
  }

  &.credit {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.2);

    .amount-label {
      color: #16a34a;
    }

    .amount-value {
      color: #15803d;
    }
  }
}

.amount-label {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.amount-value {
  font-size: 1.3rem;
  font-weight: 700;
}

// Responsive Design
@media screen and (max-width: 1200px) {
  .filter-group {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.2rem;
  }

  .date-filters {
    grid-template-columns: 1fr 1fr;
  }
}

@media screen and (max-width: 768px) {
  .reports-container {
    padding: 1.5rem;
    border-radius: 1.5rem;
  }

  .page-title {
    .title-text {
      font-size: 2rem;
    }

    .title-icon {
      font-size: 2.4rem;
    }
  }

  .form-section,
  .table-section {
    padding: 1.5rem;
    border-radius: 1.2rem;
  }

  .filter-group {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .date-filters {
    grid-template-columns: 1fr;
  }

  .submit-btn {
    padding: 1rem 2.5rem;
    font-size: 1.3rem;
  }

  // Show mobile cards, hide desktop table
  .desktop-table {
    display: none;
  }

  .mobile-cards {
    display: block;
  }

  ::ng-deep .custom-multiselect {
    .p-multiselect-label {
      font-size: 1.2rem;
      padding: 0.8rem 1rem;
    }

    .p-multiselect-item {
      font-size: 1.2rem;
      padding: 0.6rem 1rem;
    }

    .p-multiselect-panel {
      min-width: 250px;
    }

    .p-multiselect-filter {
      height: 2.5rem;
      font-size: 1.2rem;
    }
  }

  ::ng-deep .date-picker .ant-picker {
    height: 4rem;

    .ant-picker-input input {
      font-size: 1.2rem;
      padding: 0.8rem 1rem;
    }
  }
}

@media screen and (max-width: 600px) {
  .reports-container {
    padding: 1rem;
    border-radius: 1.2rem;
  }

  .page-title {
    flex-direction: column;
    gap: 0.5rem;

    .title-text {
      font-size: 1.8rem;
      text-align: center;
    }

    .title-icon {
      font-size: 2rem;
    }
  }

  .form-section,
  .table-section {
    padding: 1rem;
    border-radius: 1rem;
  }

  .filter-label {
    font-size: 1.2rem;

    .label-icon {
      font-size: 1.4rem;
    }
  }

  .submit-btn {
    padding: 0.8rem 2rem;
    font-size: 1.2rem;
    border-radius: 1rem;

    .btn-icon {
      font-size: 1.4rem;
    }
  }

  .table-title {
    font-size: 1.6rem;

    .table-icon {
      font-size: 1.8rem;
    }
  }

  .report-card {
    padding: 1.2rem;
    border-radius: 1rem;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.8rem;

    .document-number {
      font-size: 1.3rem;
    }

    .document-type {
      font-size: 1rem;
      padding: 0.3rem 0.6rem;
    }
  }

  .amounts-row {
    grid-template-columns: 1fr;
    gap: 0.8rem;
  }

  .amount-item {
    padding: 0.8rem;

    .amount-label {
      font-size: 0.9rem;
    }

    .amount-value {
      font-size: 1.2rem;
    }
  }

  ::ng-deep .custom-multiselect {
    .p-multiselect-label {
      font-size: 1.1rem;
      padding: 0.7rem 0.8rem;
    }

    .p-multiselect-item {
      font-size: 1.1rem;
      padding: 0.5rem 0.8rem;
    }

    .p-multiselect-filter {
      height: 2.2rem;
      font-size: 1.1rem;
      padding: 0.6rem 0.8rem;
    }

    .p-multiselect-filter-icon {
      font-size: 1.2rem;

      svg {
        width: 1.2rem;
        height: 1.2rem;
      }
    }
  }

  ::ng-deep .date-picker .ant-picker {
    height: 3.5rem;

    .ant-picker-input input {
      font-size: 1.1rem;
      padding: 0.7rem 0.8rem;
    }
  }
}

@media screen and (max-width: 480px) {
  .reports-container {
    padding: 0.8rem;
    border-radius: 1rem;
  }

  .page-title .title-text {
    font-size: 1.6rem;
  }

  .form-section,
  .table-section {
    padding: 0.8rem;
  }

  .submit-btn {
    width: 100%;
    justify-content: center;
  }

  .report-card {
    padding: 1rem;
  }

  .info-row,
  .date-row {
    flex-direction: column;
    gap: 0.3rem;

    .label {
      min-width: auto;
      font-size: 1rem;
    }

    .value {
      text-align: left;
      font-size: 1rem;
    }
  }
}

// RTL Support
[dir="rtl"] {
  .page-title {
    flex-direction: row-reverse;
  }

  .filter-label {
    flex-direction: row-reverse;
  }

  .submit-btn {
    flex-direction: row-reverse;
  }

  .table-title {
    flex-direction: row-reverse;
  }

  .card-header {
    flex-direction: row-reverse;
  }

  .info-row,
  .date-row {
    flex-direction: row-reverse;

    .value {
      text-align: left;
    }
  }

  @media screen and (max-width: 600px) {
    .card-header {
      flex-direction: column-reverse;
      align-items: flex-end;
    }

    .info-row,
    .date-row {
      flex-direction: column-reverse;

      .value {
        text-align: right;
      }
    }
  }
}
